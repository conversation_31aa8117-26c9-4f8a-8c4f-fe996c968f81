[workspace]
resolver = "2"
members = ["infer", "core", "server", "app", "common"]

[workspace.package]
version = "0.1.0"
edition = "2024"

[[bin]]
name = "daemon"
path = "src/daemon/bin/main.rs"

[[bin]]
name = "client"
path = "src/client/bin/main.rs"




[workspace.dependencies]
auto-live-common = { path = "common" }

# Core dependencies
tokio = "1.45"
serde = "1.0"
serde_json = "1.0"
chrono = "0.4"
anyhow = "1.0"
reqwest = "0.12"
futures = "0.3"

# Serialization
ciborium = "0.2"
ciborium-io = "0.2"

# Audio processing
wav_io = "0.1"
symphonia = "0.5"
symphonia-core = "0.5"
rodio = "0.20"
lofty = "0.22"

# Networking and RPC
iroh = "0.35"
iroh-relay = "0.35"
iroh-base = "0.35"
iroh-dns-server = "0.35"
iroh-net-report = "0.34"
iroh-blobs = "0.35"
iroh-gossip = "0.35"
iroh-docs = "0.35"
tarpc = "0.36"

# Async utilities
n0-future = "0.1"
futures-core = "0.3"
futures-sink = "0.3"
futures-util = "0.3"
futures-lite = "2.6"
tokio-util = "0.7"
tokio-serde = "0.9"
tokio-stream = "0.1"
tokio-tungstenite = "0.26"
async-stream = "0.3"
pin-project = "1.1"

# Web framework
axum = "0.8"
axum-extra = "0.10"
tower = "0.5"
tower-http = "0.6"
http = "1.3"
headers = "0.4"
tungstenite = "0.26"

# Database
sea-orm = "1.1"

# Utilities
uuid = { version = "1.17", default-features = false }
derive_more = "2.0"
once_cell = "1.21"
itertools = "0.14"
url = "2.5"
hex = "0.4"
blake2 = "0.10"
arc-swap = "1.7"
rand = "0.9"

# Logging and tracing
env_logger = "0.11"
log = "0.4"
tracing = "0.1"
tracing-subscriber = "0.3"

# Build dependencies
built = "0.8"

# App-specific dependencies
dioxus = "0.7.0-alpha.1"
dioxus-html = "0.7.0-alpha.1"
dioxus-signals = "0.7.0-alpha.1"
dioxus-web = "0.7.0-alpha.1"
dioxus-desktop = "0.7.0-alpha.1"
dioxus-mobile = "0.7.0-alpha.1"
dioxus-logger = "0.7.0-alpha.1"
manganis = "0.7.0-alpha.1"
opendal = { version = "0.53", default-features = false }
jwt-compact = "0.8"
thiserror = "2.0"
async-once-cell = "0.5"
dotenv_codegen = "0.15"
sevenz-rust2 = "0.13"
gloo-storage = "0.3"
gloo-timers = "0.3"
web-sys = "0.3"
js-sys = "0.3"
wasm-bindgen-futures = "0.4"
sysinfo = "0.35"
dirs = "6"

[profile]

[profile.wasm-dev]
inherits = "dev"
opt-level = 1

[profile.server-dev]
inherits = "dev"

[profile.android-dev]
inherits = "dev"
